import { isMobileOnly } from "react-device-detect";
import { HistoryBox } from "../components";
import ReportBuilder from "../components/ReportBuilder";
import "./styles.scss";

const Reports = () => {
  return (
    <main className="reports-section d-flex flex-lg-row flex-column align-items-stretch w-100">
      {!isMobileOnly && <HistoryBox />}
      <ReportBuilder />
    </main>
  );
};

export default Reports;
