import { create } from "zustand";

interface ReportBlock {
  title: string;
  content: string;
  sortOrder: number;
}

interface PayloadInterface {
  reportInfo: {
    title: string;
    blocks: ReportBlock[];
  };
}

const initialState = {
  reportInfo: {
    title: "",
    blocks: [
      {
        sortOrder: 0,
        title: "Introduction",
        content: "",
      },
    ],
  },
};

const reportStore = (set: any) => ({
  ...initialState,
  setReportTitle: (data: string) =>
    set((state: PayloadInterface) => ({
      ...state,
      reportInfo: { ...state.reportInfo, title: data },
    })),
  setReportBlocks: (data: ReportBlock) =>
    set((state: PayloadInterface) => ({
      ...state,
      reportInfo: {
        ...state.reportInfo,
        blocks: [...state.reportInfo.blocks, data],
      },
    })),
  addReportBlockAtIndex: (data: ReportBlock, index: number) =>
    set((state: PayloadInterface) => {
      const newBlocks = [...state.reportInfo.blocks];
      newBlocks.splice(index + 1, 0, {
        ...data,
        sortOrder: index + 1,
      });
      for (let i = index + 2; i < newBlocks.length; i++) {
        newBlocks[i].sortOrder = i;
      }
      return {
        ...state,
        reportInfo: {
          ...state.reportInfo,
          blocks: newBlocks,
        },
      };
    }),
  removeReportBlockAtIndex: (index: number) =>
    set((state: PayloadInterface) => {
      const newBlocks = [...state.reportInfo.blocks];
      newBlocks.splice(index, 1);
      for (let i = index; i < newBlocks.length; i++) {
        newBlocks[i].sortOrder = i;
      }
      return {
        ...state,
        reportInfo: {
          ...state.reportInfo,
          blocks: newBlocks,
        },
      };
    }),
  swapReportBlocks: (index1: number, index2: number) =>
    set((state: PayloadInterface) => {
      const newBlocks = [...state.reportInfo.blocks];
      if (
        index1 >= 0 &&
        index1 < newBlocks.length &&
        index2 >= 0 &&
        index2 < newBlocks.length
      ) {
        // Store the content and title to swap
        const block1Title = newBlocks[index1].title;
        const block1Content = newBlocks[index1].content;
        const block2Title = newBlocks[index2].title;
        const block2Content = newBlocks[index2].content;

        // Swap only the content and title, keep sortOrder tied to position
        newBlocks[index1] = {
          ...newBlocks[index1],
          title: block2Title,
          content: block2Content,
          sortOrder: index1, // Keep sortOrder tied to position
        };

        newBlocks[index2] = {
          ...newBlocks[index2],
          title: block1Title,
          content: block1Content,
          sortOrder: index2, // Keep sortOrder tied to position
        };
      }
      return {
        ...state,
        reportInfo: {
          ...state.reportInfo,
          blocks: newBlocks,
        },
      };
    }),
  updateReportBlockTitle: (index: number, title: string) =>
    set((state: PayloadInterface) => {
      const newBlocks = [...state.reportInfo.blocks];
      if (index >= 0 && index < newBlocks.length) {
        newBlocks[index] = {
          ...newBlocks[index],
          title: title,
        };
      }
      return {
        ...state,
        reportInfo: {
          ...state.reportInfo,
          blocks: newBlocks,
        },
      };
    }),
  updateReportBlockContent: (index: number, content: any) =>
    set((state: PayloadInterface) => {
      const newBlocks = [...state.reportInfo.blocks];
      if (index >= 0 && index < newBlocks.length) {
        newBlocks[index] = {
          ...newBlocks[index],
          content: content,
        };
      }
      return {
        ...state,
        reportInfo: {
          ...state.reportInfo,
          blocks: newBlocks,
        },
      };
    }),
  resetReportState: () => set(() => initialState),
});

// const useReportStore: any = create(
//   devtools(
//     persist(reportStore, {
//       name: "report",
//     })
//   )
// );

const useReportStore = create(reportStore);

export const {
  setReportTitle,
  setReportBlocks,
  addReportBlockAtIndex,
  removeReportBlockAtIndex,
  swapReportBlocks,
  updateReportBlockTitle,
  updateReportBlockContent,
  resetReportState,
} = useReportStore.getState();

export default useReportStore;
