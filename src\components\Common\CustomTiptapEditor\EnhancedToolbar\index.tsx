import {
  RiAlignCenter,
  RiAlignJustify,
  RiAlignLeft,
  RiAlignRight,
  RiArrowGoBackFill,
  RiArrowGoForwardFill,
  RiBold,
  RiCodeBoxFill,
  RiDoubleQuotesL,
  RiFormatClear,
  RiH1,
  RiH2,
  RiH3,
  RiItalic,
  RiListOrdered,
  RiListUnordered,
  RiParagraph,
  RiSeparator,
  RiStrikethrough,
  RiUnderline,
} from "@remixicon/react";
import { Editor } from "@tiptap/react";
import EditorToolbar from "features/Reports/components/ReportBuilder/SectionBlock/EditorToolbar";
import { Button, ButtonGroup } from "react-bootstrap";
import "./styles.scss";

interface EnhancedToolbarProps {
  editor: Editor | null;
  onSave: () => void;
}

const EnhancedToolbar = ({ editor, onSave }: EnhancedToolbarProps) => {
  if (!editor) {
    return null;
  }

  // Undo/Redo actions
  const handleUndo = () => editor.chain().focus().undo().run();
  const handleRedo = () => editor.chain().focus().redo().run();

  // Text formatting actions
  const handleBold = () => editor.chain().focus().toggleBold().run();
  const handleItalic = () => editor.chain().focus().toggleItalic().run();
  const handleUnderline = () => editor.chain().focus().toggleUnderline().run();
  const handleStrikethrough = () => editor.chain().focus().toggleStrike().run();
  const handleCode = () => editor.chain().focus().toggleCode().run();
  const handleClearFormatting = () =>
    editor.chain().focus().clearNodes().unsetAllMarks().run();

  // Heading actions
  const handleHeading1 = () =>
    editor.chain().focus().toggleHeading({ level: 1 }).run();
  const handleHeading2 = () =>
    editor.chain().focus().toggleHeading({ level: 2 }).run();
  const handleHeading3 = () =>
    editor.chain().focus().toggleHeading({ level: 3 }).run();
  const handleParagraph = () => editor.chain().focus().setParagraph().run();

  // List actions
  const handleBulletList = () =>
    editor.chain().focus().toggleBulletList().run();
  const handleOrderedList = () =>
    editor.chain().focus().toggleOrderedList().run();

  // Block actions
  const handleBlockquote = () =>
    editor.chain().focus().toggleBlockquote().run();
  const handleCodeBlock = () => editor.chain().focus().toggleCodeBlock().run();
  const handleHorizontalRule = () =>
    editor.chain().focus().setHorizontalRule().run();

  // Table actions
  const handleAddRowBefore = () => editor.chain().focus().addRowBefore().run();
  const handleAddRowAfter = () => editor.chain().focus().addRowAfter().run();
  const handleDeleteRow = () => editor.chain().focus().deleteRow().run();
  const handleAddColumnBefore = () =>
    editor.chain().focus().addColumnBefore().run();
  const handleAddColumnAfter = () =>
    editor.chain().focus().addColumnAfter().run();
  const handleDeleteColumn = () => editor.chain().focus().deleteColumn().run();
  const handleDeleteTable = () => editor.chain().focus().deleteTable().run();
  const handleMergeCells = () => editor.chain().focus().mergeCells().run();

  const isInTable = editor.isActive("table");

  console.log(editor.getJSON())

  return (
    <div className="enhanced-toolbar sticky-top bg-white border-bottom shadow-sm px-3">
      <div className="toolbar-container d-flex flex-wrap align-items-center gap-2 p-3">
        <ButtonGroup>
          <Button
            variant="outline-secondary"
            size="sm"
            onClick={handleUndo}
            title="Undo"
            disabled={!editor.can().undo()}
          >
            <RiArrowGoBackFill size={18} />
          </Button>

          <Button
            variant="outline-secondary"
            size="sm"
            onClick={handleRedo}
            title="Redo"
            disabled={!editor.can().redo()}
          >
            <RiArrowGoForwardFill size={18} />
          </Button>
          <Button
            variant={editor.isActive("bold") ? "primary" : "outline-secondary"}
            size="sm"
            onClick={handleBold}
            title="Bold"
          >
            <RiBold size={16} />
          </Button>
          <Button
            variant={
              editor.isActive("italic") ? "primary" : "outline-secondary"
            }
            size="sm"
            onClick={handleItalic}
            title="Italic"
          >
            <RiItalic size={16} />
          </Button>
          <Button
            variant={
              editor.isActive("underline") ? "primary" : "outline-secondary"
            }
            size="sm"
            onClick={handleUnderline}
            title="Underline"
          >
            <RiUnderline size={16} />
          </Button>
          <Button
            variant={
              editor.isActive("strike") ? "primary" : "outline-secondary"
            }
            size="sm"
            onClick={handleStrikethrough}
            title="Strikethrough"
          >
            <RiStrikethrough size={16} />
          </Button>
          <Button
            variant={editor.isActive("code") ? "primary" : "outline-secondary"}
            size="sm"
            onClick={handleCode}
            title="Inline Code"
          >
            <RiCodeBoxFill size={16} />
          </Button>
          <Button
            variant="outline-secondary"
            size="sm"
            onClick={handleClearFormatting}
            title="Clear Formatting"
          >
            <RiFormatClear size={16} />
          </Button>
          <Button
            variant={
              editor.isActive("paragraph") ? "primary" : "outline-secondary"
            }
            size="sm"
            onClick={handleParagraph}
            title="Paragraph"
          >
            <RiParagraph size={16} />
          </Button>
          <Button
            variant={
              editor.isActive("heading", { level: 1 })
                ? "primary"
                : "outline-secondary"
            }
            size="sm"
            onClick={handleHeading1}
            title="Heading 1"
          >
            <RiH1 size={16} />
          </Button>
          <Button
            variant={
              editor.isActive("heading", { level: 2 })
                ? "primary"
                : "outline-secondary"
            }
            size="sm"
            onClick={handleHeading2}
            title="Heading 2"
          >
            <RiH2 size={16} />
          </Button>
          <Button
            variant={
              editor.isActive("heading", { level: 3 })
                ? "primary"
                : "outline-secondary"
            }
            size="sm"
            onClick={handleHeading3}
            title="Heading 3"
          >
            <RiH3 size={16} />
          </Button>
          <Button
            variant={
              editor.isActive("bulletList") ? "primary" : "outline-secondary"
            }
            size="sm"
            onClick={handleBulletList}
            title="Bullet List"
          >
            <RiListUnordered size={16} />
          </Button>
          <Button
            variant={
              editor.isActive("orderedList") ? "primary" : "outline-secondary"
            }
            size="sm"
            onClick={handleOrderedList}
            title="Numbered List"
          >
            <RiListOrdered size={16} />
          </Button>
          <Button
            variant={
              editor.isActive("blockquote") ? "primary" : "outline-secondary"
            }
            size="sm"
            onClick={handleBlockquote}
            title="Quote"
          >
            <RiDoubleQuotesL size={16} />
          </Button>
          <Button
            variant={
              editor.isActive("codeBlock") ? "primary" : "outline-secondary"
            }
            size="sm"
            onClick={handleCodeBlock}
            title="Code Block"
          >
            <RiCodeBoxFill size={16} />
          </Button>
          <Button
            variant="outline-secondary"
            size="sm"
            onClick={handleHorizontalRule}
            title="Horizontal Rule"
          >
            <RiSeparator size={16} />
          </Button>
          <Button
            variant={
              editor.isActive({ textAlign: "left" })
                ? "primary"
                : "outline-secondary"
            }
            size="sm"
            onClick={() => editor.chain().focus().setTextAlign("left").run()}
            title="Align Left"
          >
            <RiAlignLeft size={16} />
          </Button>

          <Button
            variant={
              editor.isActive({ textAlign: "center" })
                ? "primary"
                : "outline-secondary"
            }
            size="sm"
            onClick={() => editor.chain().focus().setTextAlign("center").run()}
            title="Align Center"
          >
            <RiAlignCenter size={16} />
          </Button>

          <Button
            variant={
              editor.isActive({ textAlign: "right" })
                ? "primary"
                : "outline-secondary"
            }
            size="sm"
            onClick={() => editor.chain().focus().setTextAlign("right").run()}
            title="Align Right"
          >
            <RiAlignRight size={16} />
          </Button>

          <Button
            variant={
              editor.isActive({ textAlign: "justify" })
                ? "primary"
                : "outline-secondary"
            }
            size="sm"
            onClick={() => editor.chain().focus().setTextAlign("justify").run()}
            title="Justify"
          >
            <RiAlignJustify size={16} />
          </Button>
          <EditorToolbar editor={editor} onSave={onSave} />
        </ButtonGroup>

        {/* Table Actions - Only show when in table */}
        {isInTable && (
          <div className="d-flex gap-3 mt-2">
            <Button
              onClick={handleAddRowBefore}
              style={{
                fontSize: "12px",
                padding: "0.25rem 0.5rem",
                backgroundColor: "#f0f0f0",
                borderColor: "#ccc",
                color: "#000",
                fontWeight: "bold",
              }}
            >
              Add Row Before
            </Button>
            <Button
              style={{
                fontSize: "12px",
                padding: "0.25rem 0.5rem",
                backgroundColor: "#f0f0f0",
                borderColor: "#ccc",
                color: "#000",
                fontWeight: "bold",
              }}
              onClick={handleAddRowAfter}
            >
              Add Row After
            </Button>
            <Button
              style={{
                fontSize: "12px",
                padding: "0.25rem 0.5rem",
                backgroundColor: "#f0f0f0",
                borderColor: "#ccc",
                color: "#000",
                fontWeight: "bold",
              }}
              onClick={handleDeleteRow}
            >
              Delete Row
            </Button>
            <Button
              style={{
                fontSize: "12px",
                padding: "0.25rem 0.5rem",
                backgroundColor: "#f0f0f0",
                borderColor: "#ccc",
                color: "#000",
                fontWeight: "bold",
              }}
              onClick={handleAddColumnBefore}
            >
              Add Column Before
            </Button>
            <Button
              style={{
                fontSize: "12px",
                padding: "0.25rem 0.5rem",
                backgroundColor: "#f0f0f0",
                borderColor: "#ccc",
                color: "#000",
                fontWeight: "bold",
              }}
              onClick={handleAddColumnAfter}
            >
              Add Column After
            </Button>
            <Button
              style={{
                fontSize: "12px",
                padding: "0.25rem 0.5rem",
                backgroundColor: "#f0f0f0",
                borderColor: "#ccc",
                color: "#000",
                fontWeight: "bold",
              }}
              onClick={handleDeleteColumn}
            >
              Delete Column
            </Button>
            <Button
              style={{
                fontSize: "12px",
                padding: "0.25rem 0.5rem",
                backgroundColor: "#f0f0f0",
                borderColor: "#ccc",
                color: "#000",
                fontWeight: "bold",
              }}
              onClick={handleDeleteTable}
            >
              Delete Table
            </Button>
            <Button
              style={{
                fontSize: "12px",
                padding: "0.25rem 0.5rem",
                backgroundColor: "#f0f0f0",
                borderColor: "#ccc",
                color: "#000",
                fontWeight: "bold",
              }}
              onClick={handleMergeCells}
            >
              Merge Cells
            </Button>
          </div>
        )}
      </div>
    </div>
  );
};

export default EnhancedToolbar;
